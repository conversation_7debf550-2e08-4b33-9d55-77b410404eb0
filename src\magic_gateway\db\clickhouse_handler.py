"""ClickHouse handler for the MagicGateway application."""

import asyncio
import os
import tempfile
from typing import Any, Dict, List, Optional, Tuple, AsyncGenerator

# Keep using the synchronous driver client type hint
from starlette.concurrency import run_in_threadpool  # Still needed for sync driver

from magic_gateway.core.config import settings
from magic_gateway.core.exceptions import ClickHouseException
from magic_gateway.core.logging_config import log

# Import the manager instance
from magic_gateway.db.connection_manager import clickhouse_connection_manager


class ClickHouseHandler:
    """Handler for ClickHouse database operations using the connection manager."""

    @staticmethod
    async def execute_command(
        command: str,
        params: Optional[Dict[str, Any]] = None,
        query_id: Optional[str] = None,
        # Remove client parameter - manager handles connections
    ) -> None:
        """
        Execute a command against ClickHouse using a connection from the pool.

        Args:
            command: SQL command to execute
            params: Command parameters
            query_id: Optional ID for tracking the command
        """
        command_type = (
            command.strip().split(maxsplit=1)[0].upper()
            if command.strip()
            else "UNKNOWN"
        )
        log_context = f"(ID: {query_id})" if query_id else ""
        log.info(f"Executing ClickHouse {command_type} command {log_context}")

        # Log details at DEBUG level
        if settings.LOG_LEVEL.upper() == "DEBUG":
            # Simplified logging for brevity, retain truncation if needed
            log.debug(
                f"Command {log_context}: {command[:200]}{'...' if len(command) > 200 else ''}"
            )
            if params:
                log.debug(
                    f"Params {log_context}: {str(params)[:200]}{'...' if len(str(params)) > 200 else ''}"
                )

        # Use the connection manager's context manager
        try:
            # Acquire connection from the pool using async with
            async with clickhouse_connection_manager.connection() as client:
                # Execute the command in a threadpool because the driver is synchronous
                settings_dict = {}
                if query_id:
                    settings_dict["query_id"] = query_id

                # Add execution timeout setting from config
                settings_dict["max_execution_time"] = settings.MAX_QUERY_EXECUTION_TIME

                # Create a wrapper function to ensure all parameters are passed correctly
                def execute_command_with_settings():
                    return client.execute(
                        command,
                        params=params or {},  # Ensure params is a dict
                        settings=settings_dict,
                        # types_check=True # Optional: enable type checking
                    )

                await run_in_threadpool(execute_command_with_settings)
                log.info(f"ClickHouse command executed successfully {log_context}")

        except ClickHouseException as e:
            # Catch exceptions from the connection manager or execution
            log.error(f"ClickHouse command failed {log_context}: {e}", exc_info=True)
            # Re-raise the specific exception type or a generic one if needed
            raise  # Keep original exception type (e.g., PoolTimeout or ExecutionError)
        except Exception as e:
            # Catch unexpected errors
            log.error(
                f"Unexpected error executing ClickHouse command {log_context}: {e}",
                exc_info=True,
            )
            raise ClickHouseException(
                f"Unexpected error during ClickHouse command {log_context}: {e}"
            ) from e
        # No finally block needed to release connection, context manager handles it

    @staticmethod
    async def execute_query(
        query: str,
        params: Optional[Dict[str, Any]] = None,
        query_id: Optional[str] = None,
        allow_write: bool = False,  # Keep flag for read-only check
        # Remove client parameter - manager handles connections
    ) -> Tuple[List[Dict[str, Any]], List[str]]:
        """
        Execute a query against ClickHouse using a connection from the pool.

        Args:
            query: SQL query to execute
            params: Query parameters
            query_id: Optional ID for tracking the query
            allow_write: If False, checks if query seems read-only.

        Returns:
            Tuple containing:
                - List of dictionaries representing rows
                - List of column names
        """
        # Read-only check
        if not allow_write:
            query_lower_stripped = query.strip().lower()
            # More robust check for read-only queries
            read_only_starts = (
                "select",
                "show",
                "describe",
                "exists",
                "explain",
                "with",
            )
            is_read_query = query_lower_stripped.startswith(read_only_starts) or (
                query_lower_stripped.startswith("(")
                and "select" in query_lower_stripped
            )  # CTEs often start with (

            if not is_read_query:
                # Check for potentially harmful keywords if not starting with known read-only
                write_keywords = {
                    "insert",
                    "create",
                    "alter",
                    "drop",
                    "truncate",
                    "optimize",
                    "attach",
                    "detach",
                    "system",
                }
                query_tokens = set(query_lower_stripped.split())
                if any(keyword in query_tokens for keyword in write_keywords):
                    raise ClickHouseException(
                        "Query appears to be a write operation, but allow_write=False. "
                        "Use execute_command for modifications or set allow_write=True if this is intended (e.g., SELECT with side effects)."
                    )
                # If it doesn't start with read-only and doesn't contain obvious write keywords, log a warning?
                # log.warning(f"Query does not start with typical read keyword but allow_write=False: {query[:100]}...")

        query_type = (
            query.strip().split(maxsplit=1)[0].upper() if query.strip() else "UNKNOWN"
        )
        log_context = f"(ID: {query_id})" if query_id else ""
        log.info(f"Executing ClickHouse {query_type} query {log_context}")

        # Log details at DEBUG level
        if settings.LOG_LEVEL.upper() == "DEBUG":
            log.debug(
                f"Query {log_context}: {query[:200]}{'...' if len(query) > 200 else ''}"
            )
            if params:
                log.debug(
                    f"Params {log_context}: {str(params)[:200]}{'...' if len(str(params)) > 200 else ''}"
                )

        start_time = asyncio.get_event_loop().time()

        try:
            # Acquire connection from the pool using async with
            async with clickhouse_connection_manager.connection() as client:
                # Execute the query in a threadpool
                settings_dict = {
                    "max_execution_time": settings.MAX_QUERY_EXECUTION_TIME,
                    # Consider adding timeout_before_checking_execution_speed if needed
                }
                if query_id:
                    settings_dict["query_id"] = query_id

                # Add buffer setting for large results if applicable
                # settings_dict['receive_buffer_size'] = 1048576 # Example: 1MB

                # We need results with column names/types
                # Create a wrapper function to ensure all parameters are passed correctly
                def execute_query_with_settings():
                    return client.execute(
                        query,
                        params=params or {},
                        # query_id can be passed directly or via settings, settings is preferred
                        # query_id=query_id,
                        with_column_types=True,
                        settings=settings_dict,
                        # types_check=True # Optional
                    )

                result_with_types = await run_in_threadpool(execute_query_with_settings)

            end_time = asyncio.get_event_loop().time()
            execution_time = end_time - start_time

            # Process results
            rows, column_info = result_with_types
            column_names = [col_name for col_name, _col_type in column_info]

            # Convert rows to list of dictionaries more efficiently
            result_dicts = [dict(zip(column_names, row)) for row in rows]

            log.info(
                f"ClickHouse query {log_context} completed in {execution_time:.3f}s, "
                f"returned {len(result_dicts)} rows."
            )

            return result_dicts, column_names

        except ClickHouseException as e:
            # Catch exceptions from connection manager or execution
            log.error(f"ClickHouse query failed {log_context}: {e}", exc_info=True)
            raise  # Keep original exception type
        except Exception as e:
            # Catch unexpected errors
            log.error(
                f"Unexpected error executing ClickHouse query {log_context}: {e}",
                exc_info=True,
            )
            raise ClickHouseException(
                f"Unexpected error during ClickHouse query {log_context}: {e}"
            ) from e
        # No finally block needed for connection release

    @staticmethod
    async def stream_query_results(
        query: str,
        params: Optional[Dict[str, Any]] = None,
        query_id: Optional[str] = None,
        allow_write: bool = False,
        chunk_size: int = 100000,
    ) -> AsyncGenerator[List[Dict[str, Any]], None]:
        """
        Stream query results from ClickHouse in chunks to avoid memory issues.

        Args:
            query: SQL query to execute
            params: Query parameters
            query_id: Optional ID for tracking the query
            allow_write: If False, checks if query seems read-only
            chunk_size: Number of rows to process at a time

        Yields:
            Chunks of data as lists of dictionaries
        """
        # Read-only check (same as execute_query)
        if not allow_write:
            query_lower_stripped = query.strip().lower()
            read_only_starts = (
                "select",
                "show",
                "describe",
                "exists",
                "explain",
                "with",
            )
            is_read_query = query_lower_stripped.startswith(read_only_starts) or (
                query_lower_stripped.startswith("(")
                and "select" in query_lower_stripped
            )

            if not is_read_query:
                write_keywords = {
                    "insert",
                    "create",
                    "alter",
                    "drop",
                    "truncate",
                    "optimize",
                    "attach",
                    "detach",
                    "system",
                }
                query_tokens = set(query_lower_stripped.split())
                if any(keyword in query_tokens for keyword in write_keywords):
                    raise ClickHouseException(
                        "Query appears to be a write operation, but allow_write=False. "
                        "Use execute_command for modifications or set allow_write=True if this is intended."
                    )

        query_type = (
            query.strip().split(maxsplit=1)[0].upper() if query.strip() else "UNKNOWN"
        )
        log_context = f"(ID: {query_id})" if query_id else ""
        log.info(f"Streaming ClickHouse {query_type} query {log_context}")

        # Log details at DEBUG level
        if settings.LOG_LEVEL.upper() == "DEBUG":
            log.debug(
                f"Query {log_context}: {query[:200]}{'...' if len(query) > 200 else ''}"
            )
            if params:
                log.debug(
                    f"Params {log_context}: {str(params)[:200]}{'...' if len(str(params)) > 200 else ''}"
                )

        start_time = asyncio.get_event_loop().time()
        total_rows = 0
        column_names = None

        try:
            # Acquire connection from the pool
            async with clickhouse_connection_manager.connection() as client:
                # Execute the query with settings
                settings_dict = {
                    "max_execution_time": settings.MAX_QUERY_EXECUTION_TIME,
                }
                if query_id:
                    settings_dict["query_id"] = query_id

                # We need to use execute_iter for streaming
                # First, get the column names by executing with limit 0
                def get_column_info():
                    return client.execute(
                        f"{query} LIMIT 0",
                        params=params or {},
                        with_column_types=True,
                        settings=settings_dict,
                    )

                # Get column info from a query with LIMIT 0
                _, column_info = await run_in_threadpool(get_column_info)
                column_names = [col_name for col_name, _ in column_info]

                # Now execute the actual query with execute_iter
                def create_query_iterator():
                    return client.execute_iter(
                        query,
                        params=params or {},
                        settings=settings_dict,
                    )

                # Get the iterator
                iterator = await run_in_threadpool(create_query_iterator)

                # Process results in chunks
                chunk = []

                # Define a function to process a chunk of rows
                async def process_chunk(rows_chunk):
                    # Convert rows to list of dictionaries
                    return [dict(zip(column_names, row)) for row in rows_chunk]

                # Process the iterator in chunks
                while True:
                    # Get the next chunk of rows
                    try:
                        # Get rows from the iterator in a thread pool
                        def get_next_rows():
                            rows = []
                            for _ in range(chunk_size):
                                try:
                                    rows.append(next(iterator))
                                except StopIteration:
                                    break
                            return rows

                        # Get the next batch of rows
                        rows = await run_in_threadpool(get_next_rows)

                        if not rows:
                            break

                        # Process the chunk
                        chunk = await process_chunk(rows)
                        total_rows += len(chunk)

                        # Yield the chunk
                        yield chunk

                        # Add a small delay to allow other requests to be processed
                        # This prevents the worker from being completely tied up
                        await asyncio.sleep(0.01)

                    except StopIteration:
                        break

                # Close the iterator
                def close_iterator():
                    try:
                        iterator.close()
                    except Exception as e:
                        log.warning(f"Error closing iterator: {e}")

                await run_in_threadpool(close_iterator)

            # Log completion
            end_time = asyncio.get_event_loop().time()
            execution_time = end_time - start_time
            log.info(
                f"ClickHouse streaming query {log_context} completed in {execution_time:.3f}s, "
                f"streamed {total_rows} rows."
            )

        except ClickHouseException as e:
            # Catch exceptions from connection manager or execution
            log.error(
                f"ClickHouse streaming query failed {log_context}: {e}", exc_info=True
            )
            raise  # Keep original exception type
        except Exception as e:
            # Catch unexpected errors
            log.error(
                f"Unexpected error executing ClickHouse streaming query {log_context}: {e}",
                exc_info=True,
            )
            raise ClickHouseException(
                f"Unexpected error during ClickHouse streaming query {log_context}: {e}"
            ) from e

    @staticmethod
    async def export_to_parquet_file(
        query: str,
        output_file_path: str,
        params: Optional[Dict[str, Any]] = None,
        query_id: Optional[str] = None,
        allow_write: bool = False,
    ) -> Tuple[str, int]:
        """
        Export query results directly to a Parquet file using ClickHouse's native functionality.

        This method uses ClickHouse's INTO OUTFILE functionality to export data directly
        to Parquet format, which is more efficient than streaming rows and converting.

        Args:
            query: SQL query to execute
            output_file_path: Path where the Parquet file should be created
            params: Query parameters
            query_id: Optional ID for tracking the query
            allow_write: If False, checks if query seems read-only

        Returns:
            Tuple of (file_path, row_count)

        Raises:
            ClickHouseException: If the export fails
        """
        log_context = f"(ID: {query_id})" if query_id else ""
        log.info(f"Starting ClickHouse native Parquet export {log_context}")

        # Validate query if write protection is enabled
        if not allow_write:
            query_upper = query.strip().upper()
            write_keywords = [
                "INSERT",
                "UPDATE",
                "DELETE",
                "DROP",
                "CREATE",
                "ALTER",
                "TRUNCATE",
            ]
            if any(keyword in query_upper for keyword in write_keywords):
                raise ClickHouseException(
                    f"Query appears to contain write operations but allow_write=False {log_context}"
                )

        try:
            # Ensure output directory exists
            os.makedirs(os.path.dirname(output_file_path), exist_ok=True)

            # Build the export query using ClickHouse's INTO OUTFILE
            export_query = f"""
                {query}
                INTO OUTFILE '{output_file_path}'
                FORMAT Parquet
            """

            # Execute the export query
            async with clickhouse_connection_manager.connection() as client:
                settings_dict = {
                    "max_execution_time": settings.MAX_QUERY_EXECUTION_TIME,
                }
                if query_id:
                    settings_dict["query_id"] = query_id

                def execute_export():
                    return client.execute(
                        export_query,
                        params=params or {},
                        settings=settings_dict,
                    )

                # Execute the export
                await run_in_threadpool(execute_export)

                # Get row count by executing a count query
                count_query = f"SELECT count(*) FROM ({query})"

                def get_row_count():
                    result = client.execute(
                        count_query,
                        params=params or {},
                        settings=settings_dict,
                    )
                    return result[0][0] if result and result[0] else 0

                row_count = await run_in_threadpool(get_row_count)

                log.info(
                    f"ClickHouse native Parquet export completed {log_context}: "
                    f"{row_count} rows exported to {output_file_path}"
                )

                return output_file_path, row_count

        except Exception as e:
            log.error(
                f"ClickHouse native Parquet export failed {log_context}: {e}",
                exc_info=True,
            )
            # Clean up partial file if it exists
            if os.path.exists(output_file_path):
                try:
                    os.remove(output_file_path)
                except Exception as cleanup_error:
                    log.warning(
                        f"Failed to clean up partial export file: {cleanup_error}"
                    )

            raise ClickHouseException(
                f"Failed to export data to Parquet file {log_context}: {e}"
            ) from e

    @staticmethod
    async def export_table_to_parquet(
        table_name: str,
        query_id: Optional[str] = None,
        temp_dir: Optional[str] = None,
    ) -> Tuple[str, int]:
        """
        Export an entire table to a temporary Parquet file using ClickHouse's native functionality.

        Args:
            table_name: Name of the table to export (e.g., "kpi_results.result_table_123")
            query_id: Optional ID for tracking the query
            temp_dir: Optional directory for temporary files (defaults to system temp)

        Returns:
            Tuple of (temp_file_path, row_count)

        Raises:
            ClickHouseException: If the export fails
        """
        log_context = f"(ID: {query_id})" if query_id else ""
        log.info(
            f"Starting ClickHouse table export to Parquet {log_context}: {table_name}"
        )

        # Create temporary file
        temp_dir = temp_dir or tempfile.gettempdir()
        temp_file = tempfile.NamedTemporaryFile(
            delete=False,
            suffix=".parquet",
            dir=temp_dir,
            prefix=f"ch_export_{query_id or 'unknown'}_",
        )
        temp_file_path = temp_file.name
        temp_file.close()

        try:
            # Use the export_to_parquet_file method with a simple SELECT * query
            query = f"SELECT * FROM {table_name}"
            return await ClickHouseHandler.export_to_parquet_file(
                query=query,
                output_file_path=temp_file_path,
                query_id=query_id,
                allow_write=False,
            )

        except Exception as e:
            # Clean up temp file on error
            if os.path.exists(temp_file_path):
                try:
                    os.remove(temp_file_path)
                except Exception as cleanup_error:
                    log.warning(f"Failed to clean up temp export file: {cleanup_error}")

            raise ClickHouseException(
                f"Failed to export table {table_name} to Parquet {log_context}: {e}"
            ) from e
