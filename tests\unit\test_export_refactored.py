"""
Tests for the refactored export functionality using ClickHouse native Parquet export.

This module tests the new export approach that uses ClickHouse's INTO OUTFILE
functionality to export data to Parquet format first, then processes the Parquet
file for different output formats.
"""

import os
import tempfile
import pytest
from unittest.mock import AsyncMock, MagicMock, patch, mock_open
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from fastapi.responses import StreamingResponse, FileResponse

from magic_gateway.db.clickhouse_handler import ClickHouseHandler
from magic_gateway.utils.parquet_processor import (
    read_parquet_file_in_chunks,
    read_parquet_file_async,
    create_parquet_file_response,
    convert_parquet_to_csv_stream,
    cleanup_parquet_file,
    safe_cleanup_parquet_files,
    get_parquet_file_info,
)


class TestClickHouseNativeExport:
    """Test ClickHouse native Parquet export functionality."""

    @pytest.mark.asyncio
    async def test_export_to_parquet_file_success(self):
        """Test successful export to Parquet file."""
        with patch('magic_gateway.db.clickhouse_handler.clickhouse_connection_manager') as mock_manager:
            # Mock the connection manager and client
            mock_client = AsyncMock()
            mock_manager.connection.return_value.__aenter__.return_value = mock_client
            
            # Mock the execute method
            mock_client.execute.return_value = None
            
            # Mock os.makedirs
            with patch('os.makedirs') as mock_makedirs:
                # Mock the count query result
                with patch.object(ClickHouseHandler, 'execute_query', return_value=([[1000]], [])):
                    result_path, row_count = await ClickHouseHandler.export_to_parquet_file(
                        query="SELECT * FROM test_table",
                        output_file_path="/tmp/test.parquet",
                        query_id="test_query_123"
                    )
                    
                    assert result_path == "/tmp/test.parquet"
                    assert row_count == 1000
                    mock_makedirs.assert_called_once()

    @pytest.mark.asyncio
    async def test_export_to_parquet_file_write_protection(self):
        """Test that write operations are blocked when allow_write=False."""
        with pytest.raises(Exception) as exc_info:
            await ClickHouseHandler.export_to_parquet_file(
                query="INSERT INTO test_table VALUES (1, 'test')",
                output_file_path="/tmp/test.parquet",
                allow_write=False
            )
        
        assert "write operations" in str(exc_info.value).lower()

    @pytest.mark.asyncio
    async def test_export_table_to_parquet_success(self):
        """Test successful table export to temporary Parquet file."""
        with patch.object(ClickHouseHandler, 'export_to_parquet_file') as mock_export:
            mock_export.return_value = ("/tmp/temp_file.parquet", 500)
            
            result_path, row_count = await ClickHouseHandler.export_table_to_parquet(
                table_name="kpi_results.test_table",
                query_id="test_query_456"
            )
            
            assert result_path == "/tmp/temp_file.parquet"
            assert row_count == 500
            mock_export.assert_called_once()

    @pytest.mark.asyncio
    async def test_export_table_to_parquet_cleanup_on_error(self):
        """Test that temporary files are cleaned up on error."""
        with patch('tempfile.NamedTemporaryFile') as mock_temp:
            mock_temp_file = MagicMock()
            mock_temp_file.name = "/tmp/test_temp.parquet"
            mock_temp.return_value = mock_temp_file
            
            with patch.object(ClickHouseHandler, 'export_to_parquet_file') as mock_export:
                mock_export.side_effect = Exception("Export failed")
                
                with patch('os.path.exists', return_value=True):
                    with patch('os.remove') as mock_remove:
                        with pytest.raises(Exception):
                            await ClickHouseHandler.export_table_to_parquet(
                                table_name="kpi_results.test_table",
                                query_id="test_query_error"
                            )
                        
                        mock_remove.assert_called_once_with("/tmp/test_temp.parquet")


class TestParquetProcessor:
    """Test Parquet file processing utilities."""

    def test_read_parquet_file_in_chunks(self):
        """Test reading Parquet file in chunks."""
        # Mock pyarrow components
        with patch('pyarrow.parquet.read_table') as mock_read_table:
            # Create a mock table
            mock_table = MagicMock()
            mock_table.__len__.return_value = 250000  # 250k rows
            mock_table.slice.return_value.to_pylist.return_value = [
                {"id": 1, "name": "test1"},
                {"id": 2, "name": "test2"}
            ]
            mock_read_table.return_value = mock_table
            
            chunks = read_parquet_file_in_chunks("/tmp/test.parquet", chunk_size=100000)
            
            assert len(chunks) == 3  # 250k rows / 100k chunk_size = 3 chunks
            mock_read_table.assert_called_once_with("/tmp/test.parquet")

    @pytest.mark.asyncio
    async def test_read_parquet_file_async(self):
        """Test asynchronous Parquet file reading."""
        with patch('magic_gateway.utils.parquet_processor.read_parquet_file_in_chunks') as mock_read:
            mock_read.return_value = [
                [{"id": 1, "name": "test1"}],
                [{"id": 2, "name": "test2"}]
            ]
            
            chunks = []
            async for chunk in read_parquet_file_async("/tmp/test.parquet"):
                chunks.append(chunk)
            
            assert len(chunks) == 2
            assert chunks[0] == [{"id": 1, "name": "test1"}]
            assert chunks[1] == [{"id": 2, "name": "test2"}]

    def test_create_parquet_file_response(self):
        """Test creating FileResponse for Parquet file."""
        response = create_parquet_file_response(
            file_path="/tmp/test.parquet",
            filename="export_data",
            cleanup_file=False
        )
        
        assert isinstance(response, FileResponse)
        assert response.path == "/tmp/test.parquet"
        assert "export_data.parquet" in response.headers["content-disposition"]

    @pytest.mark.asyncio
    async def test_convert_parquet_to_csv_stream(self):
        """Test converting Parquet to CSV stream."""
        with patch('magic_gateway.utils.parquet_processor.read_parquet_file_async') as mock_read:
            mock_read.return_value = iter([
                [{"id": 1, "name": "test1"}, {"id": 2, "name": "test2"}]
            ])
            
            csv_chunks = []
            async for chunk in convert_parquet_to_csv_stream("/tmp/test.parquet"):
                csv_chunks.append(chunk)
            
            assert len(csv_chunks) == 1
            assert "id,name" in csv_chunks[0]  # Header
            assert "1,test1" in csv_chunks[0]  # Data row

    def test_cleanup_parquet_file(self):
        """Test Parquet file cleanup."""
        with patch('os.path.exists', return_value=True):
            with patch('os.remove') as mock_remove:
                cleanup_parquet_file("/tmp/test.parquet")
                mock_remove.assert_called_once_with("/tmp/test.parquet")

    def test_cleanup_parquet_file_not_exists(self):
        """Test cleanup when file doesn't exist."""
        with patch('os.path.exists', return_value=False):
            with patch('os.remove') as mock_remove:
                cleanup_parquet_file("/tmp/nonexistent.parquet")
                mock_remove.assert_not_called()

    def test_safe_cleanup_parquet_files(self):
        """Test safe cleanup of multiple files."""
        with patch('magic_gateway.utils.parquet_processor.cleanup_parquet_file') as mock_cleanup:
            safe_cleanup_parquet_files("/tmp/file1.parquet", "/tmp/file2.parquet", None)
            
            assert mock_cleanup.call_count == 2
            mock_cleanup.assert_any_call("/tmp/file1.parquet")
            mock_cleanup.assert_any_call("/tmp/file2.parquet")

    def test_get_parquet_file_info(self):
        """Test getting Parquet file information."""
        with patch('pyarrow.parquet.ParquetFile') as mock_parquet_file:
            with patch('os.path.getsize', return_value=1024):
                # Mock ParquetFile metadata
                mock_file = MagicMock()
                mock_file.metadata.num_rows = 1000
                mock_file.metadata.num_columns = 5
                mock_file.metadata.num_row_groups = 2
                mock_file.schema_arrow.names = ["id", "name", "value", "date", "status"]
                mock_file.schema_arrow.__str__ = lambda: "schema_info"
                mock_parquet_file.return_value = mock_file
                
                info = get_parquet_file_info("/tmp/test.parquet")
                
                assert info["file_size_bytes"] == 1024
                assert info["num_rows"] == 1000
                assert info["num_columns"] == 5
                assert info["num_row_groups"] == 2
                assert len(info["column_names"]) == 5


class TestExportEndpointIntegration:
    """Integration tests for the refactored export endpoint."""

    @pytest.mark.asyncio
    async def test_export_endpoint_uses_results_metadata_table(self):
        """Test that export endpoint queries the results_metadata table."""
        # This would be an integration test that verifies the endpoint
        # queries the correct table structure
        pass

    @pytest.mark.asyncio
    async def test_export_endpoint_native_parquet_flow(self):
        """Test the complete native Parquet export flow."""
        # This would test the full flow from endpoint to response
        pass

    @pytest.mark.asyncio
    async def test_export_endpoint_error_handling(self):
        """Test error handling and cleanup in export endpoint."""
        # This would test that temporary files are cleaned up on errors
        pass
