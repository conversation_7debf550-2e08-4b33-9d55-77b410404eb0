"""
Integration tests for the refactored export functionality.

These tests verify that the complete export flow works correctly with the
new ClickHouse native Parquet export approach and results_metadata table.
"""

import pytest
import tempfile
import os
from unittest.mock import patch, AsyncMock, MagicMock
from fastapi.testclient import TestClient
from fastapi import FastAPI

# Import the main application or router
# from magic_gateway.main import app  # Adjust import as needed
from magic_gateway.api.v1.endpoints.scripts import router as scripts_router


@pytest.fixture
def test_app():
    """Create a test FastAPI application."""
    app = FastAPI()
    app.include_router(scripts_router, prefix="/api/v1/scripts")
    return app


@pytest.fixture
def client(test_app):
    """Create a test client."""
    return TestClient(test_app)


class TestExportEndpointIntegration:
    """Integration tests for the export endpoint."""

    def test_export_endpoint_exists(self, client):
        """Test that the export endpoint exists and is accessible."""
        # This is a basic connectivity test
        response = client.get("/api/v1/scripts/export/job/123")
        # We expect some response (even if it's an error due to missing data)
        # The important thing is that the endpoint is reachable
        assert response.status_code in [200, 404, 422, 500]  # Any valid HTTP response

    @patch('magic_gateway.db.clickhouse_handler.ClickHouseHandler.execute_query')
    @patch('magic_gateway.db.clickhouse_handler.ClickHouseHandler.export_table_to_parquet')
    def test_export_csv_format_integration(self, mock_export_parquet, mock_execute_query, client):
        """Test CSV export format integration."""
        # Mock the metadata query response (results_metadata table)
        mock_execute_query.return_value = (
            [["test_result_table", "Test Analysis", '{"test": "data"}']],
            ["final_result_table", "analysis_name", "job_info"]
        )
        
        # Mock the Parquet export
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".parquet")
        temp_file.close()
        mock_export_parquet.return_value = (temp_file.name, 1000)
        
        # Mock Parquet file reading
        with patch('magic_gateway.utils.parquet_processor.convert_parquet_to_csv_stream') as mock_csv_stream:
            mock_csv_stream.return_value = iter(["id,name\n1,test\n2,test2\n"])
            
            try:
                response = client.get("/api/v1/scripts/export/job/123?format=csv")
                
                # Verify the response
                assert response.status_code == 200
                assert response.headers["content-type"] == "text/csv; charset=utf-8"
                assert "attachment" in response.headers.get("content-disposition", "")
                
                # Verify that the correct methods were called
                mock_execute_query.assert_called()
                mock_export_parquet.assert_called_once()
                
            finally:
                # Clean up temp file
                if os.path.exists(temp_file.name):
                    os.remove(temp_file.name)

    @patch('magic_gateway.db.clickhouse_handler.ClickHouseHandler.execute_query')
    @patch('magic_gateway.db.clickhouse_handler.ClickHouseHandler.export_table_to_parquet')
    def test_export_parquet_format_integration(self, mock_export_parquet, mock_execute_query, client):
        """Test Parquet export format integration."""
        # Mock the metadata query response
        mock_execute_query.return_value = (
            [["test_result_table", "Test Analysis", '{"test": "data"}']],
            ["final_result_table", "analysis_name", "job_info"]
        )
        
        # Mock the Parquet export
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".parquet")
        temp_file.close()
        mock_export_parquet.return_value = (temp_file.name, 1000)
        
        try:
            response = client.get("/api/v1/scripts/export/job/123?format=parquet")
            
            # Verify the response
            assert response.status_code == 200
            assert "application/octet-stream" in response.headers["content-type"]
            assert "attachment" in response.headers.get("content-disposition", "")
            assert ".parquet" in response.headers.get("content-disposition", "")
            
            # Verify that the correct methods were called
            mock_execute_query.assert_called()
            mock_export_parquet.assert_called_once()
            
        finally:
            # Clean up temp file
            if os.path.exists(temp_file.name):
                os.remove(temp_file.name)

    @patch('magic_gateway.db.clickhouse_handler.ClickHouseHandler.execute_query')
    @patch('magic_gateway.db.clickhouse_handler.ClickHouseHandler.export_table_to_parquet')
    def test_export_excel_format_integration(self, mock_export_parquet, mock_execute_query, client):
        """Test Excel export format integration."""
        # Mock the metadata query response
        mock_execute_query.return_value = (
            [["test_result_table", "Test Analysis", '{"test": "data"}']],
            ["final_result_table", "analysis_name", "job_info"]
        )
        
        # Mock the Parquet export
        temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".parquet")
        temp_file.close()
        mock_export_parquet.return_value = (temp_file.name, 1000)
        
        # Mock Parquet file reading for Excel conversion
        with patch('magic_gateway.utils.parquet_processor.read_parquet_file_async') as mock_read_async:
            mock_read_async.return_value = iter([[{"id": 1, "name": "test"}]])
            
            # Mock Excel response generation
            with patch('magic_gateway.utils.generate_non_blocking_excel_response') as mock_excel_response:
                mock_response = MagicMock()
                mock_response.status_code = 200
                mock_excel_response.return_value = mock_response
                
                try:
                    response = client.get("/api/v1/scripts/export/job/123?format=excel")
                    
                    # Verify that the correct methods were called
                    mock_execute_query.assert_called()
                    mock_export_parquet.assert_called_once()
                    mock_excel_response.assert_called_once()
                    
                finally:
                    # Clean up temp file
                    if os.path.exists(temp_file.name):
                        os.remove(temp_file.name)

    @patch('magic_gateway.db.clickhouse_handler.ClickHouseHandler.execute_query')
    def test_export_job_not_found(self, mock_execute_query, client):
        """Test export when job is not found in results_metadata table."""
        # Mock empty result from metadata query
        mock_execute_query.return_value = ([], [])
        
        response = client.get("/api/v1/scripts/export/job/999?format=csv")
        
        # Should return 404 when job is not found
        assert response.status_code == 404
        assert "not found" in response.json()["detail"].lower()

    @patch('magic_gateway.db.clickhouse_handler.ClickHouseHandler.execute_query')
    def test_export_database_error_handling(self, mock_execute_query, client):
        """Test export error handling when database operations fail."""
        # Mock database error
        from magic_gateway.core.exceptions import ClickHouseException
        mock_execute_query.side_effect = ClickHouseException("Database connection failed")
        
        response = client.get("/api/v1/scripts/export/job/123?format=csv")
        
        # Should return 500 for database errors
        assert response.status_code == 500
        assert "database error" in response.json()["detail"].lower()

    def test_export_invalid_format(self, client):
        """Test export with invalid format parameter."""
        response = client.get("/api/v1/scripts/export/job/123?format=invalid_format")
        
        # Should return 422 for invalid format
        assert response.status_code == 422

    def test_export_invalid_job_id(self, client):
        """Test export with invalid job ID."""
        response = client.get("/api/v1/scripts/export/job/invalid_id?format=csv")
        
        # Should return 422 for invalid job ID
        assert response.status_code == 422


class TestResultsMetadataTableUsage:
    """Test that the export functionality correctly uses the results_metadata table."""

    @patch('magic_gateway.db.clickhouse_handler.ClickHouseHandler.execute_query')
    def test_queries_results_metadata_table(self, mock_execute_query, client):
        """Test that the export endpoint queries the results_metadata table."""
        # Mock successful metadata query
        mock_execute_query.return_value = (
            [["test_result_table", "Test Analysis", '{"test": "data"}']],
            ["final_result_table", "analysis_name", "job_info"]
        )
        
        # Mock the Parquet export to avoid actual file operations
        with patch('magic_gateway.db.clickhouse_handler.ClickHouseHandler.export_table_to_parquet') as mock_export:
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=".parquet")
            temp_file.close()
            mock_export.return_value = (temp_file.name, 1000)
            
            try:
                response = client.get("/api/v1/scripts/export/job/123?format=parquet")
                
                # Verify that execute_query was called with results_metadata table
                mock_execute_query.assert_called()
                call_args = mock_execute_query.call_args[1]  # Get keyword arguments
                query = call_args.get('query', '')
                
                # Verify the query references results_metadata table
                assert "kpi_results.results_metadata" in query
                assert "job_id = '123'" in query
                
            finally:
                # Clean up temp file
                if os.path.exists(temp_file.name):
                    os.remove(temp_file.name)
