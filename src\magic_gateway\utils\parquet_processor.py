"""
Parquet file processing utilities for the MagicGateway application.

This module provides functions to read and process Parquet files created by
ClickHouse's native export functionality.
"""

import os
import tempfile
from typing import Any, Dict, List, Optional, AsyncGenerator, Tuple
import asyncio
from concurrent.futures import ThreadPoolExecutor

import pyarrow as pa
import pyarrow.parquet as pq
from fastapi.responses import StreamingResponse, FileResponse
from starlette.background import BackgroundTask

from magic_gateway.core.logging_config import log


def read_parquet_file_in_chunks(
    file_path: str, chunk_size: int = 100000
) -> List[List[Dict[str, Any]]]:
    """
    Read a Parquet file in chunks and return as list of dictionaries.

    Args:
        file_path: Path to the Parquet file
        chunk_size: Number of rows per chunk

    Returns:
        List of chunks, where each chunk is a list of dictionaries

    Raises:
        Exception: If the file cannot be read
    """
    try:
        log.info(f"Reading Parquet file: {file_path}")

        # Read the Parquet file
        table = pq.read_table(file_path)
        total_rows = len(table)

        log.info(f"Parquet file contains {total_rows} rows")

        # Convert to list of dictionaries in chunks
        chunks = []
        for start_idx in range(0, total_rows, chunk_size):
            end_idx = min(start_idx + chunk_size, total_rows)
            chunk_table = table.slice(start_idx, end_idx - start_idx)
            chunk_dict = chunk_table.to_pylist()
            chunks.append(chunk_dict)

        log.info(f"Split Parquet data into {len(chunks)} chunks")
        return chunks

    except Exception as e:
        log.error(f"Error reading Parquet file {file_path}: {e}", exc_info=True)
        raise


async def read_parquet_file_async(
    file_path: str, chunk_size: int = 100000
) -> AsyncGenerator[List[Dict[str, Any]], None]:
    """
    Asynchronously read a Parquet file in chunks.

    Args:
        file_path: Path to the Parquet file
        chunk_size: Number of rows per chunk

    Yields:
        Chunks of data as lists of dictionaries
    """
    try:
        log.info(f"Starting async Parquet file read: {file_path}")

        # Use thread pool to avoid blocking the event loop
        with ThreadPoolExecutor() as executor:
            loop = asyncio.get_event_loop()
            chunks = await loop.run_in_executor(
                executor, read_parquet_file_in_chunks, file_path, chunk_size
            )

        # Yield chunks asynchronously
        for chunk in chunks:
            yield chunk
            # Allow other tasks to run
            await asyncio.sleep(0)

    except Exception as e:
        log.error(f"Error in async Parquet file read: {e}", exc_info=True)
        # Yield empty chunk on error
        yield []


def create_parquet_file_response(
    file_path: str, filename: str, cleanup_file: bool = True
) -> FileResponse:
    """
    Create a FileResponse for a Parquet file with optional cleanup.

    Args:
        file_path: Path to the Parquet file
        filename: Name for the downloaded file
        cleanup_file: Whether to delete the file after sending

    Returns:
        FileResponse for the Parquet file
    """
    # Create background task to clean up file if requested
    background_task = None
    if cleanup_file:

        def cleanup():
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    log.info(f"Cleaned up temporary Parquet file: {file_path}")
            except Exception as e:
                log.warning(f"Failed to clean up Parquet file {file_path}: {e}")

        background_task = BackgroundTask(cleanup)

    return FileResponse(
        path=file_path,
        filename=f"{filename}.parquet",
        media_type="application/octet-stream",
        background=background_task,
    )


async def convert_parquet_to_csv_stream(
    file_path: str, chunk_size: int = 100000
) -> AsyncGenerator[str, None]:
    """
    Convert a Parquet file to CSV format as a streaming response.

    Args:
        file_path: Path to the Parquet file
        chunk_size: Number of rows per chunk

    Yields:
        CSV data as strings
    """
    import csv
    import io

    try:
        log.info(f"Converting Parquet to CSV stream: {file_path}")

        # Read the first chunk to get headers
        first_chunk_yielded = False

        async for chunk in read_parquet_file_async(file_path, chunk_size):
            if not chunk:
                continue

            # Create CSV string for this chunk
            output = io.StringIO()
            writer = csv.DictWriter(output, fieldnames=chunk[0].keys())

            # Write header only for the first chunk
            if not first_chunk_yielded:
                writer.writeheader()
                first_chunk_yielded = True

            # Write rows
            writer.writerows(chunk)

            # Yield the CSV data
            csv_data = output.getvalue()
            yield csv_data

            # Allow other tasks to run
            await asyncio.sleep(0)

    except Exception as e:
        log.error(f"Error converting Parquet to CSV: {e}", exc_info=True)
        # Yield empty string on error
        yield ""


def get_parquet_file_info(file_path: str) -> Dict[str, Any]:
    """
    Get information about a Parquet file.

    Args:
        file_path: Path to the Parquet file

    Returns:
        Dictionary with file information
    """
    try:
        # Read Parquet file metadata
        parquet_file = pq.ParquetFile(file_path)

        # Get basic info
        info = {
            "file_path": file_path,
            "file_size_bytes": os.path.getsize(file_path),
            "num_rows": parquet_file.metadata.num_rows,
            "num_columns": parquet_file.metadata.num_columns,
            "num_row_groups": parquet_file.metadata.num_row_groups,
            "schema": str(parquet_file.schema_arrow),
            "column_names": parquet_file.schema_arrow.names,
        }

        return info

    except Exception as e:
        log.error(f"Error getting Parquet file info: {e}", exc_info=True)
        return {"file_path": file_path, "error": str(e)}


def cleanup_parquet_file(file_path: str) -> None:
    """
    Clean up a temporary Parquet file.

    Args:
        file_path: Path to the file to clean up
    """
    try:
        if file_path and os.path.exists(file_path):
            os.remove(file_path)
            log.info(f"Cleaned up Parquet file: {file_path}")
        elif file_path:
            log.debug(f"Parquet file does not exist for cleanup: {file_path}")
    except Exception as e:
        log.warning(f"Failed to clean up Parquet file {file_path}: {e}")


def safe_cleanup_parquet_files(*file_paths: str) -> None:
    """
    Safely clean up multiple Parquet files, continuing even if some fail.

    Args:
        *file_paths: Variable number of file paths to clean up
    """
    for file_path in file_paths:
        if file_path:
            cleanup_parquet_file(file_path)
