#!/usr/bin/env python3
"""
Test script to verify the PyArrow compatibility fix for ClickHouse Parquet export.

This script tests that the ParquetWriter initialization works correctly without
the unsupported row_group_size parameter and handles different PyArrow versions.
"""

import sys
import os
import tempfile
import asyncio
from pathlib import Path

# Add the src directory to the Python path
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

async def test_pyarrow_compatibility():
    """Test PyArrow compatibility fixes."""
    print("🔧 Testing PyArrow Compatibility Fix")
    print("=" * 50)
    
    try:
        # Import required modules
        import pyarrow as pa
        import pyarrow.parquet as pq
        print(f"✅ PyArrow version: {pa.__version__}")
        
        # Test 1: Test ParquetWriter with different parameter combinations
        print("\n1. Testing ParquetWriter parameter compatibility...")
        
        # Create test data
        test_data = [
            {"id": 1, "name": "test1", "value": 100.5},
            {"id": 2, "name": "test2", "value": 200.7},
            {"id": 3, "name": "test3", "value": 300.9},
        ]
        table = pa.Table.from_pylist(test_data)
        schema = table.schema
        
        # Test with full parameters (should work with our fix)
        temp_file1 = tempfile.NamedTemporaryFile(suffix=".parquet", delete=False)
        temp_path1 = temp_file1.name
        temp_file1.close()
        
        try:
            writer1 = pq.ParquetWriter(
                temp_path1,
                schema,
                compression="snappy",
                write_statistics=True,
            )
            writer1.write_table(table)
            writer1.close()
            
            # Verify file
            assert os.path.exists(temp_path1)
            file_size1 = os.path.getsize(temp_path1)
            assert file_size1 > 0
            
            # Verify readability
            read_table1 = pq.read_table(temp_path1)
            assert len(read_table1) == 3
            
            print(f"✅ Full parameters work ({file_size1} bytes)")
            
        except Exception as e:
            print(f"❌ Full parameters failed: {e}")
            return False
        finally:
            try:
                os.remove(temp_path1)
            except:
                pass
        
        # Test with minimal parameters (fallback)
        temp_file2 = tempfile.NamedTemporaryFile(suffix=".parquet", delete=False)
        temp_path2 = temp_file2.name
        temp_file2.close()
        
        try:
            writer2 = pq.ParquetWriter(temp_path2, schema)
            writer2.write_table(table)
            writer2.close()
            
            # Verify file
            assert os.path.exists(temp_path2)
            file_size2 = os.path.getsize(temp_path2)
            assert file_size2 > 0
            
            # Verify readability
            read_table2 = pq.read_table(temp_path2)
            assert len(read_table2) == 3
            
            print(f"✅ Minimal parameters work ({file_size2} bytes)")
            
        except Exception as e:
            print(f"❌ Minimal parameters failed: {e}")
            return False
        finally:
            try:
                os.remove(temp_path2)
            except:
                pass
        
        # Test 2: Verify the fixed ClickHouse handler
        print("\n2. Testing ClickHouse handler compatibility...")
        
        from magic_gateway.db.clickhouse_handler import ClickHouseHandler
        import inspect
        
        # Check that the method doesn't use row_group_size
        source = inspect.getsource(ClickHouseHandler.export_to_parquet_file)
        
        # Should NOT contain row_group_size parameter
        assert "row_group_size=" not in source, "Method still uses unsupported row_group_size parameter"
        
        # Should contain fallback handling
        assert "TypeError" in source, "Method should handle TypeError for parameter compatibility"
        assert "minimal parameters" in source, "Method should have fallback to minimal parameters"
        
        print("✅ ClickHouse handler uses compatible parameters")
        
        # Test 3: Test error handling improvements
        print("\n3. Testing error handling improvements...")
        
        # Should have proper cleanup
        assert "parquet_writer.close()" in source, "Should properly close ParquetWriter"
        assert "os.remove(output_file_path)" in source, "Should clean up partial files"
        assert "log.warning" in source, "Should log warnings for compatibility issues"
        
        print("✅ Error handling and cleanup are improved")
        
        # Test 4: Test PyArrow version logging
        print("\n4. Testing PyArrow version logging...")
        
        # Should log PyArrow version for debugging
        assert "pyarrow.__version__" in source, "Should log PyArrow version for debugging"
        
        print("✅ PyArrow version logging is implemented")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_compatibility_scenarios():
    """Test different compatibility scenarios."""
    print("\n🔄 Testing Compatibility Scenarios")
    print("=" * 40)
    
    try:
        import pyarrow as pa
        import pyarrow.parquet as pq
        
        # Test scenario 1: Simulate TypeError (unsupported parameter)
        print("\n1. Testing TypeError handling...")
        
        test_data = [{"id": 1, "name": "test"}]
        table = pa.Table.from_pylist(test_data)
        schema = table.schema
        
        temp_file = tempfile.NamedTemporaryFile(suffix=".parquet", delete=False)
        temp_path = temp_file.name
        temp_file.close()
        
        try:
            # This should work with our compatibility approach
            writer = pq.ParquetWriter(temp_path, schema)
            writer.write_table(table)
            writer.close()
            
            # Verify
            assert os.path.exists(temp_path)
            assert os.path.getsize(temp_path) > 0
            
            print("✅ Minimal parameter approach works")
            
        finally:
            try:
                os.remove(temp_path)
            except:
                pass
        
        # Test scenario 2: File cleanup after error
        print("\n2. Testing file cleanup after errors...")
        
        temp_file = tempfile.NamedTemporaryFile(suffix=".parquet", delete=False)
        temp_path = temp_file.name
        temp_file.close()
        
        try:
            writer = pq.ParquetWriter(temp_path, schema)
            writer.write_table(table)
            # Simulate error before close
            writer.close()
            
            # File should exist and be readable
            assert os.path.exists(temp_path)
            read_table = pq.read_table(temp_path)
            assert len(read_table) == 1
            
            print("✅ File operations work correctly")
            
        finally:
            try:
                os.remove(temp_path)
            except:
                pass
        
        return True
        
    except Exception as e:
        print(f"❌ Compatibility scenario test failed: {e}")
        return False

async def main():
    """Run all compatibility tests."""
    print("🚀 PyArrow Compatibility Fix Verification")
    print("=" * 60)
    
    # Test the compatibility fix
    compatibility_test_passed = await test_pyarrow_compatibility()
    
    # Test compatibility scenarios
    scenario_test_passed = test_compatibility_scenarios()
    
    print("\n" + "=" * 60)
    if compatibility_test_passed and scenario_test_passed:
        print("🎉 ALL TESTS PASSED! PyArrow compatibility fix is working correctly.")
        print("\nThe fix addresses:")
        print("- ❌ Removed unsupported row_group_size parameter")
        print("- ✅ Added fallback for different PyArrow versions")
        print("- ✅ Improved error handling and cleanup")
        print("- ✅ Added PyArrow version logging for debugging")
        print("- ✅ Proper ParquetWriter resource management")
        return True
    else:
        print("❌ Some tests failed. Please review the implementation.")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
